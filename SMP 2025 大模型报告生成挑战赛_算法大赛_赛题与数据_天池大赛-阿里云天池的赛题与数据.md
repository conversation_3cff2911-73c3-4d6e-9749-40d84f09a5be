2025/8/25 18:20

SMP 2025 ⼤模型报告⽣成挑战赛_算法⼤赛_赛题与数据_天池⼤赛-阿⾥云天池的赛题与数据

大模型 产品 解决方案 文档与社区 权益中心 定价 云市场 合作伙 域名

 

备案 控制台 注册

登录

天池

首页 天池大赛 天池学习 数据集 技术圈

更多

中⽂医疗信息处理评测基

个人

消息

首页 > 天池大赛 > AI大模型赛 > SMP 2025 大模型报告生成挑战赛

SMP 2025 ⼤模型报告⽣成挑战赛

赛事类型 AI⼤模型赛 奖⾦ ￥55000

团队 237

赛季2 2025-09-10

状态 进⾏中

举办⽅

已报名

查看⽐赛协议

赛制

赛题与数据

⽂档

⼤⼩

操作

ossutil命令

排⾏榜

论坛

提交结果

我的成绩

我的团队

p**************a.zip

.(8KB)

下载

复制命令

赛题与数据

1. 任务和主题

本次比赛的核心任务是构建一个以大语言模型为基础，能够根据给定题目自动生成高质量分析报告的智能系统。所有赛题均不提供用于参考

原始文本，参赛系统需完全依赖大语言模型的内部知识储备或选手自行整合的外部知识源来完成报告的撰写。

该系统需要能深刻理解题目意图，自主生成结构完整、逻辑清晰、论据合理、具有洞察力的分析报告。

初赛阶段：主要考察模型生成事实准确、结构清晰、信息全面的基础性报告的能力。题目将覆盖六大主题类别，但侧重于对已知概念

分析。

复赛阶段：将引入更具挑战性的题目，要求模型进行深度分析、趋势预测、对比论证和提出独到见解，旨在考察模型的综合推理和原

能力。

2. 比赛数据

本次比赛不提供用于检索增强生成（RAG）的特定“比赛数据集”（如新闻语料库、社交媒体数据集等）。赛题本身（即报告题目列表）即为

入数据。

参赛选手可以，并被鼓励使用任何公开可访问的外部数据、知识库或自行构建的向量数据库等技术，来增强其模型的知识能力和回答质量，

强制要求。模型的性能将完全根据其生成的最终报告质量来评判。

3. 测试数据

每个题目都将附带一个建议的英文单词数限制，参赛者应尽可能生成接近该字数的报告。

初赛测试数据 ：包含 90 道报告生成题，题目将均衡分布在以下六大主题类别中，旨在全面考察模型的多领域知识与分析能力：

前沿科技与人工智能 (Cutting-Edge Tech & AI)
商业模式与市场动态 (Business Models & Market Dynamics)
可持续发展与环境治理 (Sustainability & Environmental Governance)
社会变迁与文化趋势 (Social Change & Cultural Trends)
生命科学与公共健康 (Life Sciences & Public Health)
全球事务与未来治理 (Global Affairs & Future Governance)

复赛测试数据 ：包含 180 道报告生成题，同样分布在上述六大类别中，但题目的综合性、深度和挑战性将显著提高。

https://tianchi.aliyun.com/competition/entrance/532408/information

4. 结果提交

AI
助
理

1/4

2025/8/25 18:20

SMP 2025 ⼤模型报告⽣成挑战赛_算法⼤赛_赛题与数据_天池⼤赛-阿⾥云天池的赛题与数据

初赛结果提交 ：参赛队伍需在线提交一个UTF-8编码的 JSON文件 。该文件应包含一个JSON数组，数组中的每个元素为一个JSON
一道题目的解答。每个JSON对象必须包含以下字段：

"id" : 必须与赛题提供的题目ID完全一致。

"question" : 必须与赛题提供的报告题目原文完全一致。

"type" : 必须与赛题提供的题目类别完全一致。

"word_limit" : 必须与赛题提供的目标单词数完全一致。
"answer" : 选手模型生成的报告全文。

复赛结果提交 ：提交格式与初赛完全相同。复赛结束后，优胜队伍需要提交完整的代码、模型架构及详细的技术报告。

5. 评价指标

单题评分

每道赛题的满分为1 分 , 单题的得分由客观指标和大模型评审两个部分加权组合。

客观指标 :
此部分将通过自动化程序，从单词数匹配度、内容的丰富性、语言的专业性、文本的内在质量等多个可量化维度，对报告进行基础性
处的“单词数”指以空格为分隔符进行统计的单词数量，不包含标点符号，字数越接近目标分数越高。)
大模型评审 :
此部分将采用前沿的大语言模型作为智能评审员，模拟领域专家的视角，对报告的逻辑深度、论证质量、观点洞察力及整体完成度进

分。

最终得分将是上述两部分分数的加权总和，例如：最终得分 = 0.25 * 客观指标得分 + 0.75 * 大模型评审得分。每道赛题的满分为 1 分 。

总分计算与排名

参赛队伍在每个阶段（初赛/复赛）的最终成绩，是该阶段所有题目得分的总和 。

例如，若初赛共有90道题目，每题满分为1分，则该参赛队伍的初赛理论满分即为90分。

为便于排行榜的展示和排名，系统会将计算出的总分进行处理，并最终以一个0到1之间的数值呈现在排行榜上。排行榜将根据此最终成绩进

6. 其他需求

为确保评测的公平性与可复现性，所有晋级及获奖队伍均须提交一份完整的技术文档，并附上全部源代码及所用数据集。该技术文档应清晰

的 运行环境 、 源码组织结构 、各代码文件的具体作用以及 核心模块的设计思路 。若使用了外部或自建数据集，文档中必须一并提供其构

用方法的详细说明。此外，任何已知的潜在问题或方案局限性也请一并注明。请注意，未按要求提交完整材料的队伍，举办方将保留酌情扣

消其获奖资格的权利。

比赛数据示例

1. 初赛阶段

初赛样例集： 无
初赛测试集 :

[
    {
        "id": "smp25_pre_001",
        "question": "A Report on the Current State and Challenges of Explainable AI (XAI) Technology",
        "type": "Cutting-Edge Tech & AI",
        "word_limit": 1091
    },
    {
        "id": "smp25_pre_016",
        "question": "An Analysis of the Sustainability of the Live E-commerce Model within the 'Influencer Economy'",
        "type": "Business Models & Market Dynamics",
        "word_limit": 1151
    }
]

(考察点：要求模型围绕明确的技术或商业概念，准确梳理其现状、挑战与应用，并控制输出在目标字数附近，主要考察信息整合、清晰阐述

制的能力。)

2. 复赛阶段

复赛样例集：无
复赛测试集 :

17[
    {
        "id": "smp25_final_011",
        "question": "Forecasting the Structural Impact of AI on the Labor Market from 2025 to 2030",

https://tianchi.aliyun.com/competition/entrance/532408/information

AI
助
理

2/4

2025/8/25 18:20

SMP 2025 ⼤模型报告⽣成挑战赛_算法⼤赛_赛题与数据_天池⼤赛-阿⾥云天池的赛题与数据

        "type": "Cutting-Edge Tech & AI",
        "word_limit": 920
    },
    {
        "id": "smp25_final_174",
        "question": "An Analysis of the Possibilities and Obstacles of Establishing a Unified Global Standard for Pers
        "type": "Global Affairs & Future Governance",
        "word_limit": 1170
    }
]

(考察点：要求模型不仅要进行前瞻性预测和深度分析，还需要在更长的篇幅要求下，保持论证的逻辑严谨性和观点创新性，对模型的综合推

性思维和内容规划能力要求更高。)

评分规则解释及评测脚本

单题评分

每道赛题的满分为1 分 , 单题的得分由客观指标和大模型评审两个部分加权组合。

客观指标 :
此部分将通过自动化程序，从单词数匹配度、内容的丰富性、语言的专业性、文本的内在质量等多个可量化维度，对报告进行基础性
处的“单词数”指以空格为分隔符进行统计的单词数量，不包含标点符号，字数越接近目标分数越高。)
大模型评审 :
此部分将采用前沿的大语言模型作为智能评审员，模拟领域专家的视角，对报告的逻辑深度、论证质量、观点洞察力及整体完成度进

分。

最终得分将是上述两部分分数的加权总和，例如：最终得分 = 0.25 * 客观指标得分 + 0.75 * 大模型评审得分。每道赛题的满分为 1 分 。

总分计算与排名

参赛队伍在每个阶段（初赛/复赛）的最终成绩，是该阶段 所有题目得分的总和 。

例如，若初赛共有90道题目，每题满分为1分，则该参赛队伍的初赛理论满分即为90分。

为便于排行榜的展示和排名，系统会将计算出的总分进行处理，并最终以一个0到1之间的数值呈现在排行榜上。排行榜将根据此最终成绩进

本次比赛最终解释权归赛题主办方所有

关于我们

法务协议

联系我们 邮箱: <EMAIL>

了

为什么选择阿里云

产品和定价

解决方案

文档与社区

权益中心

支持与服务

关注阿里云

什么是云计算

全部产品

技术解决方案

文档

免费试用

基础服务

关注阿里云公众号或下载阿里云

全球基础设施

免费试用

开发者社区

解决方案免费试用

企业增值服务

技术领先

产品动态

天池大赛

高校计划

迁云服务

稳定可靠

产品定价

培训与认证

5亿算力补贴

官网公告

安全合规

配置报价器

分析师报告

云上成本管理

推荐返现计划

健康看板

信任中心

APP，关注云资讯，随时随地运

维管控云服务

联系我们：4008013260

法律声明

Cookies政策

廉正举报

安全举报

联系我们

加入我们

阿里巴巴集团 淘宝网 天猫 全球速卖通 阿里巴巴国际交易市场 1688 阿里妈妈 飞猪 阿里云计算 AliOS 万网 高德 UC 友盟 优酷 钉钉

支付宝 达摩院 淘宝海外 阿里云盘 饿了么

© 2009-2025 Aliyun.com 版权所有 增值电信业务经营许可证： 浙B2-20080101 域名注册服务机构许可： 浙D3-20210002

https://tianchi.aliyun.com/competition/entrance/532408/information

AI
助
理

3/4

2025/8/25 18:20

SMP 2025 ⼤模型报告⽣成挑战赛_算法⼤赛_赛题与数据_天池⼤赛-阿⾥云天池的赛题与数据

浙公网安备 33010602009975号 浙B2-20080101-4

https://tianchi.aliyun.com/competition/entrance/532408/information

AI
助
理

4/4